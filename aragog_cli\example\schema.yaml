# 
# This schema file is auto generated and synchronized with the database.
# Editing it will have no effect.
# 
---
version: 1612197498618
collections:
  - name: MyCollection
    is_edge_collection: false
    wait_for_sync: true
  - name: MyCollection2
    is_edge_collection: false
  - name: MyEdgeCollection
    is_edge_collection: true
    wait_for_sync: false
  - name: OrphanCollection
    is_edge_collection: false
    wait_for_sync: true
indexes:
  - name: MyIndex
    collection: MyCollection
    fields:
      - name
    settings:
      type: persistent
      unique: true
      sparse: false
      deduplicate: false
  - name: Expiration
    collection: MyCollection2
    fields:
      - expires_in
    settings:
      type: ttl
      expireAfter: 3600
  - name: Expiration
    collection: MyCollection
    fields:
      - expire_after
    settings:
      type: ttl
      expireAfter: 0
graphs:
  - name: Graph
    edgeDefinitions:
      - collection: MyEdgeCollection
        from:
          - MyCollection
        to:
          - MyCollection2
    orphanCollections:
      - OrphanCollection
    isSmart: false
    isDisjoint: true
    options:
      numberOfShards: 10
      writeConcern: 2
