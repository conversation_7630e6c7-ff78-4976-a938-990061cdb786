[package]
name = "aragog"
version = "0.17.1"
authors = [
    "<PERSON>: <<EMAIL>>",
    "Qonfucius team <<EMAIL>>"
]
edition = "2021"
description = "A simple lightweight object-document mapper for ArangoDB"
keywords = ["ArangoDB", "Arango", "ORM", "ODM", "OGM"]
exclude =  ["tests", ".gitlab-ci.yml", ".gitignore", "examples", "book", "docs", "book.toml"]
categories = ["web-programming", "database"]
license = "MIT"
readme = "README.md"
repository = "https://gitlab.com/qonfucius/aragog"
documentation = "https://docs.rs/aragog"
homepage = "https://aragog.rs"
rust-version = "1.56.1"

[features]
default = ["derive", "openssl"]
openssl = ["arangors_lite/openssl"]
rustls = ["arangors_lite/rustls"]
blocking = ["arangors_lite/blocking", "maybe-async/is_sync", "aragog-macros/blocking"]
entreprise = ["arangors_lite/enterprise"]
derive = ["aragog-macros"]
minimal_traits = []

[dependencies]
# Used for string validation toolbox, not used otherwise
regex = "1.5"

# Record trait has async methods
async-trait = "0.1"

# Serialization and deserialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.8"

# Log handling
log = "0.4"

# Error definitions
thiserror = "1.0"

# Numeric types for query engine
num = "0.4"

# Self depedencies
maybe-async = "0.2"

# Optional crates
# The aragog derive macros
aragog-macros = { path = "aragog_macros", version = "0.8", optional = true }

# ArangoDB driver
[dependencies.arangors_lite]
version = "0.2"
features = ["rocksdb"]
default-features = false

[dev-dependencies]
# We add the "feature" option to avoid serde version conflict somehow
chrono = { version = "0.4", features = ["serde"] }
# Used by examples:
env_logger = "0.9"

[dev-dependencies.tokio]
version = "1"
features = [ "macros", "rt-multi-thread"]

[badges.maintenance]
status = "actively-developed"

[workspace]
members = [
    "aragog_cli"
]