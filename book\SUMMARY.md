
# Aragog

- [Introduction](./introduction.md)
- [ArangoDB set up](./arangodb.md)
- [Installation](./installation.md)
- [Initialization](./init/index.md)
  - [Schema generation](./init/schema_generation.md)
  - [The database connection](./init/db_connection.md)
  - [Technical notes](./init/technical_notes.md)
- [The `Record` trait](./record_trait/index.md)
  - [Hooks](./record_trait/hooks.md)
  - [Technical notes](./record_trait/technical_notes.md)
- [The `Validate` trait](./validate_trait/index.md)
  - [Technical notes](./validate_trait/technical_notes.md)
- [The `EdgeRecord` struct](./edge_record_struct/index.md)
- [The query engine](./query_engine/index.md)
  - [The query object](./query_engine/query_object.md)
  - [Traversal queries](./query_engine/traversal_query.md)
- [The transactions](./transactions/index.md)
  - [Safe execution](./transactions/safe_execute.md)
  - [Custom transactions](./transactions/custom_transactions.md)
  - [Technical notes](./transactions/technical_notes.md)
    