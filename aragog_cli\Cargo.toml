[package]
name = "aragog_cli"
version = "0.6.1"
authors = [
    "<PERSON>: <<EMAIL>>",
    "Qonfucius team <<EMAIL>>"
]
edition = "2021"
license = "MIT"
description = "CLI for Aragog Crate"
exclude= ["example"]
categories = ["web-programming", "database"]
keywords = ["ArangoDB", "Arango", "CLI", "Schema", "Migrations"]
repository = "https://gitlab.com/qonfucius/aragog/-/tree/master/aragog_cli"
readme = "README.md"
homepage = "https://aragog.rs"
rust-version = "1.56.1"

[[bin]]
name = "aragog"
path = "src/main.rs"
doc = false

[dependencies]
# Aragog

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.8"
# Describe output
prettytable-rs = "0.8"
# Log
log = "0.4"

# Time
chrono = "0.4"

# Command-line args

# error
thiserror = "1.0"
exitcode = "1.1"

# CLI lib
clap_complete = "3.2"

[dependencies.clap]
version = "3.2"
features = ["derive"]

[dependencies.aragog]
path = ".."
version = "0.17"
features = ["blocking", "openssl", "minimal_traits"]
default-features = false

# ArangoDB driver
[dependencies.arangors_lite]
version = "0.2"
features = ["rocksdb", "blocking", "openssl"]
default-features = false