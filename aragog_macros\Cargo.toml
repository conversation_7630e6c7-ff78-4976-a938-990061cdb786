[package]
name = "aragog-macros"
version = "0.8.0"
authors = [
    "<PERSON>: <<EMAIL>>",
    "Qonfucius team <<EMAIL>>"
]
edition = "2021"
license = "MIT"
description = "Macros for Aragog Crate"
keywords = ["ArangoDB", "Arango"]
categories = ["database"]
repository = "https://gitlab.com/qonfucius/aragog/-/tree/master/aragog_macros"
homepage = "https://aragog.rs"
rust-version = "1.56.1"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[lib]
proc-macro = true

[features]
default = []
blocking = []

[dependencies]
syn = "1.0"
quote = "1.0"
proc-macro-error = "1.0"
proc-macro2 = "1.0"