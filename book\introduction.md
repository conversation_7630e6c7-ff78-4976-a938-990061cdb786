[![Logo](https://gitlab.com/qonfucius/aragog/-/snippets/2090578/raw/master/logo.svg)](http://aragog.rs)

# Introduction

This book covers every major feature of the [`aragog`](http://aragog.rs) library.

> Note that **everything** in the lib is documented, don't forget to check the [technical documentation](https://docs.rs/aragog)
for detailed information.

**Aragog** is an object-document mapper for [Arango DB](http://arangodb.com), including:
- Document mapping with *rust* structs
- complete **CRUD** operations with hooks (callbacks)
- Type safe query engine
- Graph and edges traversal