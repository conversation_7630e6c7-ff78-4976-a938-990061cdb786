variables:
  CARGO_HOME: $CI_PROJECT_DIR/.cargo
  RUST_BACKTRACE: 1

stages:
  - build
  - test
  - build_doc
  - deploy_doc

cache: &cache_template
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - target
    - .cargo
  policy: pull-push

.cache_pull_template: &cache_pull_template
  cache:
    <<: *cache_template
    policy: pull

build::default:
  image: rust:latest
  stage: build
  script:
    - cargo build --all
  cache:
    <<: *cache_template

build::1.56.1:
  <<: *cache_pull_template
  needs:
    - build::default
  image: rust:1.56.1
  stage: build
  script:
    - cargo build

build::features:
  image: rust:latest
  needs:
    - build::default
  stage: build
  script:
    - cargo build --features "blocking"
    - cargo build --features "minimal_traits"
    - cargo build --no-default-features
  cache:
    <<: *cache_template

build::rustls_features:
  image: rust:alpine
  needs:
    - build::default
  stage: build
  script:
    - apk add --no-cache clang musl-dev
    - cargo build --features "blocking rustls" --no-default-features
    - cargo build --features "rustls" --no-default-features
  cache:
    <<: *cache_template

.test_template: &test_template
  <<: *cache_pull_template
  variables:
    ARANGO_ROOT_PASSWORD: test
    DB_HOST: http://arangodb:8529
    DB_NAME: _system
    DB_USER: root
    DB_PASSWORD: test
    SCHEMA_PATH: tests/schema.yaml
  services:
    - arangodb:latest
  stage: test
  before_script:
    - rustup show
    - cargo -V

test:default:
  image: rust:latest
  <<: *test_template
  script:
    - cargo test --tests -- --test-threads=1

test:doc:
  image: rust:latest
  <<: *test_template
  script:
    - cargo test --doc -- --test-threads=1

test:features:
  image: rust:latest
  <<: *test_template
  script:
    - cargo test --features "derive" --no-default-features --tests -- --test-threads=1
    - cargo test --features "blocking derive" --no-default-features --tests -- --test-threads=1
    - cargo test --features "minimal_traits" --tests -- --test-threads=1

test:rustls-features:
  image: rust:alpine
  <<: *test_template
  script:
    - apk add --no-cache clang musl-dev
    - cargo test --features "rustls derive" --no-default-features --tests -- --test-threads=1
    - cargo test --features "blocking rustls derive" --no-default-features --tests -- --test-threads=1

test:packages:
  image: rust:latest
  <<: *test_template
  script:
    - cargo test --package aragog_cli --no-default-features

test:clippy:
  image: rust:latest
  <<: *test_template
  script:
    - rustup component add clippy
    - cargo clippy --all --tests -- -D warnings
    - cargo clippy --all --tests --all-features -- -D warnings

test:examples:
  image: rust:latest
  <<: *test_template
  script:
    - cargo run --example simple_example
    - cargo run --example graph_example
    - cargo run --example transaction_example
    - cargo run --example boxed_example

test::rustfmt:
  <<: *cache_pull_template
  image: rust:latest
  stage: test
  script:
    - rustup component add rustfmt
    - cargo fmt --all -- --check

rustdoc:
  <<: *cache_pull_template
  image: rust:latest
  stage: build_doc
  variables:
    PKG_NAME: aragog
  script:
    - cargo --version
    - cargo rustdoc -p $PKG_NAME --target-dir public/rustdoc -- -D warnings
  artifacts:
    paths:
      - public

build::mdbook:
  image: registry.gitlab.com/qonfucius/infrastructure/docker-images/mdbook-alpine
  stage: build_doc
  variables:
    MDBOOK_OUTPUT__HTML__SITE_URL: $CI_PAGES_URL/book
  script:
    - cargo --version
    - mdbook build -d public/book
  artifacts:
    paths:
      - public

build::website:
  image: node:lts
  stage: build_doc
  variables:
    CUSTOM_DOMAIN: http://localhost
    NODE_OPTIONS: --openssl-legacy-provider
  script:
      - cd docs/aragog-docs
      - npm ci
      - npm run generate 
      - cp -r dist ../../public
  artifacts:
    paths:
      - public

pages:
  script: ls
  needs:
    - build::mdbook
    - build::website
  stage: deploy_doc
  artifacts:
    paths:
      - public
  only:
    refs:
      - master
