# The migration files contain two sections: 
# - up: The commands to execute on migration 
# - down: The commands to execute on rollback (optional) 
# check https://docs.rs/aragog_cli for complete documentation and examples 
---
up:
  - create_index:
      name: Expiration
      fields: [ "expires_in" ]
      collection: MyCollection2
      settings:
        type: ttl
        expireAfter: 3600
  - create_collection:
      name: OrphanCollection
      wait_for_sync: true
  - create_graph:
      name: Graph
      orphan_collections:
        - OrphanCollection
      edge_definitions:
        - collection: MyEdgeCollection
          from: ["MyCollection"]
          to: ["MyCollection2"]
      is_smart: false
      is_disjoint: true
      options:
        numberOfShards: 10
        writeConcern: 2
down:
  - delete_index:
      collection: MyCollection2
      name: Expiration
  - delete_collection:
      name: OrphanCollection
  - delete_graph:
      name: Graph